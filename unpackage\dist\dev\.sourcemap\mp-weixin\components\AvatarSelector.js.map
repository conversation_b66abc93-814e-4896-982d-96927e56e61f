{"version": 3, "file": "AvatarSelector.js", "sources": ["components/AvatarSelector.vue", "D:/Program Files/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RTovd29yay9jb2RlL2VkdS9lZHUtcGVyc29uYWwtdW5pYXBwL2NvbXBvbmVudHMvQXZhdGFyU2VsZWN0b3IudnVl"], "sourcesContent": ["<template>\n  <view class=\"flex flex-col items-center\">\n    <!-- 头像显示区域 -->\n    <view\n      class=\"flex flex-col items-center cursor-pointer\"\n      @click=\"showSelector\"\n    >\n      <view class=\"relative w-20 h-20 mb-2\">\n        <!-- 自定义上传的头像 -->\n        <image\n          v-if=\"isCustomImage(currentAvatar)\"\n          :src=\"currentAvatar\"\n          class=\"w-full h-full rounded-xl object-cover\"\n          mode=\"aspectFill\"\n        />\n        <!-- 预设图标头像 -->\n        <view\n          v-else-if=\"isPresetIcon(currentAvatar)\"\n          class=\"w-full h-full rounded-xl flex items-center justify-center\"\n          :style=\"{ backgroundColor: getIconBgColor(currentAvatar) }\"\n        >\n          <i :class=\"currentAvatar\" class=\"text-white text-3xl\"></i>\n        </view>\n        <!-- 默认头像 -->\n        <view\n          v-else\n          class=\"w-full h-full rounded-xl flex items-center justify-center bg-gray-200\"\n        >\n          <i class=\"fas fa-user text-gray-400 text-3xl\"></i>\n        </view>\n\n        <!-- 编辑图标 -->\n        <view\n          class=\"absolute -bottom-0.5 -right-0.5 w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center border-2 border-white\"\n        >\n          <i class=\"fas fa-camera text-white text-xs\"></i>\n        </view>\n      </view>\n      <text class=\"text-xs text-gray-500\">点击更换头像</text>\n    </view>\n\n    <!-- 头像选择弹窗 -->\n    <view\n      v-if=\"selectorVisible\"\n      class=\"fixed inset-0 z-50 flex items-end\"\n      @click=\"closeSelector\"\n    >\n      <view class=\"absolute inset-0 bg-black bg-opacity-50\"></view>\n      <view\n        class=\"bg-white rounded-t-2xl w-full flex flex-col relative z-10\"\n        style=\"max-height: 75vh\"\n        @click.stop\n      >\n        <!-- 弹窗标题 -->\n        <view\n          class=\"flex justify-between items-center p-4 border-b border-gray-100\"\n        >\n          <text class=\"text-lg font-semibold text-gray-800\">选择头像</text>\n          <view\n            class=\"w-8 h-8 flex items-center justify-center cursor-pointer\"\n            @click=\"closeSelector\"\n          >\n            <i class=\"fas fa-times text-gray-500\"></i>\n          </view>\n        </view>\n\n        <!-- Tab切换 -->\n        <view class=\"flex border-b border-gray-100\">\n          <view\n            v-for=\"(tab, index) in tabs\"\n            :key=\"index\"\n            class=\"flex-1 flex flex-col items-center py-3 cursor-pointer transition-colors\"\n            :class=\"\n              activeTab === index\n                ? 'text-primary-500 border-b-2 border-primary-500'\n                : 'text-gray-500'\n            \"\n            @click=\"switchTab(index)\"\n          >\n            <i :class=\"tab.icon\" class=\"text-base mb-1\"></i>\n            <text class=\"text-xs\">{{ tab.name }}</text>\n          </view>\n        </view>\n\n        <!-- 内容区域 -->\n        <scroll-view\n          class=\"flex-1 p-4\"\n          scroll-y\n          style=\"max-height: calc(75vh - 140px)\"\n        >\n          <!-- 预设头像 -->\n          <view v-if=\"activeTab === 0\" class=\"space-y-4\">\n            <view\n              v-for=\"category in presetAvatars\"\n              :key=\"category.name\"\n              class=\"mb-4\"\n            >\n              <text class=\"text-sm font-semibold text-gray-700 mb-2 block\">{{\n                category.name\n              }}</text>\n              <view class=\"grid grid-cols-6 gap-2\">\n                <view\n                  v-for=\"avatar in category.items\"\n                  :key=\"avatar.id\"\n                  class=\"flex justify-center cursor-pointer\"\n                  @click=\"selectPreset(avatar)\"\n                >\n                  <view\n                    class=\"w-9 h-9 rounded-lg flex items-center justify-center transition-all relative\"\n                    :class=\"\n                      isSelected(avatar.icon)\n                        ? 'ring-2 ring-primary-500 ring-offset-1'\n                        : ''\n                    \"\n                    :style=\"{ backgroundColor: avatar.bgColor }\"\n                  >\n                    <i :class=\"avatar.icon\" class=\"text-white text-sm\"></i>\n                    <!-- 选中标识 -->\n                    <view\n                      v-if=\"isSelected(avatar.icon)\"\n                      class=\"absolute -top-1 -right-1 w-4 h-4 bg-primary-500 rounded-full flex items-center justify-center\"\n                    >\n                      <i class=\"fas fa-check text-white text-xs\"></i>\n                    </view>\n                  </view>\n                </view>\n              </view>\n            </view>\n          </view>\n\n          <!-- 自定义上传 -->\n          <view v-if=\"activeTab === 1\" class=\"flex justify-center py-10\">\n            <view @click=\"chooseImage\">\n              <view\n                class=\"flex flex-col items-center p-10 border-2 border-dashed border-gray-300 rounded-xl cursor-pointer hover:border-blue-400 hover:bg-gray-50 transition-colors\"\n              >\n                <i\n                  class=\"fas fa-cloud-upload-alt text-3xl text-gray-400 mb-3\"\n                ></i>\n                <text class=\"text-base text-gray-700 mb-2\">点击上传图片</text>\n                <text class=\"text-xs text-gray-500 text-center\"\n                  >支持 JPG、PNG 格式，建议尺寸 200x200</text\n                >\n              </view>\n            </view>\n          </view>\n        </scroll-view>\n\n        <!-- 底部按钮 -->\n        <view class=\"flex gap-3 p-4 border-t border-gray-100\">\n          <view class=\"flex-1\">\n            <button\n              class=\"w-full py-3 px-4 bg-gray-100 text-base text-gray-700 rounded-lg font-medium\"\n              @click=\"closeSelector\"\n            >\n              取消\n            </button>\n          </view>\n          <view class=\"flex-1\">\n            <button\n              class=\"w-full py-3 px-4 bg-primary-500 text-base text-white rounded-lg font-medium\"\n              @click=\"confirmSelection\"\n            >\n              确定\n            </button>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: \"AvatarSelector\",\n  props: {\n    // 头像值（字符串格式：图标类名或图片URL）\n    value: {\n      type: String,\n      default: \"fas fa-users\", // 默认图标\n    },\n    // 用于生成文字头像的文本（如群组名称）\n    text: {\n      type: String,\n      default: \"\",\n    },\n  },\n  data() {\n    return {\n      currentAvatar: this.value || \"fas fa-users\",\n      tempAvatar: null, // 临时选择的头像（字符串）\n      selectorVisible: false, // 控制弹窗显示\n      activeTab: 0,\n      tabs: [\n        { name: \"图标\", icon: \"fas fa-icons\" },\n        { name: \"上传\", icon: \"fas fa-upload\" },\n      ],\n      // 预设头像分类\n      presetAvatars: [\n        {\n          name: \"学习考试\",\n          items: [\n            { id: 1, icon: \"fas fa-graduation-cap\", bgColor: \"#3B82F6\" }, // 毕业帽\n            { id: 2, icon: \"fas fa-book\", bgColor: \"#10B981\" }, // 书本\n            { id: 3, icon: \"fas fa-pencil-alt\", bgColor: \"#F59E0B\" }, // 铅笔\n            { id: 4, icon: \"fas fa-clipboard-check\", bgColor: \"#EF4444\" }, // 考试\n            { id: 5, icon: \"fas fa-certificate\", bgColor: \"#8B5CF6\" }, // 证书\n            { id: 6, icon: \"fas fa-trophy\", bgColor: \"#FBBF24\" }, // 奖杯\n          ],\n        },\n        {\n          name: \"各行各业\",\n          items: [\n            { id: 7, icon: \"fas fa-user-md\", bgColor: \"#DC2626\" }, // 医生\n            { id: 8, icon: \"fas fa-balance-scale\", bgColor: \"#374151\" }, // 法律\n            { id: 9, icon: \"fas fa-chalkboard-teacher\", bgColor: \"#059669\" }, // 教师\n            { id: 10, icon: \"fas fa-code\", bgColor: \"#6366F1\" }, // 程序员\n            { id: 11, icon: \"fas fa-calculator\", bgColor: \"#F97316\" }, // 会计\n            { id: 12, icon: \"fas fa-hard-hat\", bgColor: \"#92400E\" }, // 工程师\n          ],\n        },\n        {\n          name: \"通用图标\",\n          items: [\n            { id: 13, icon: \"fas fa-users\", bgColor: \"#06B6D4\" }, // 群组\n            { id: 14, icon: \"fas fa-star\", bgColor: \"#FBBF24\" }, // 星星\n            { id: 15, icon: \"fas fa-lightbulb\", bgColor: \"#84CC16\" }, // 灯泡\n            { id: 16, icon: \"fas fa-fire\", bgColor: \"#DC2626\" }, // 火焰\n            { id: 17, icon: \"fas fa-rocket\", bgColor: \"#8B5CF6\" }, // 火箭\n            { id: 18, icon: \"fas fa-heart\", bgColor: \"#F87171\" }, // 爱心\n          ],\n        },\n      ],\n    };\n  },\n  watch: {\n    value: {\n      handler(newVal) {\n        this.currentAvatar = newVal || \"fas fa-users\";\n      },\n      immediate: true,\n    },\n  },\n  methods: {\n    // 判断是否为自定义图片\n    isCustomImage(avatar) {\n      if (!avatar) return false;\n      return (\n        avatar.startsWith(\"http\") ||\n        avatar.startsWith(\"data:\") ||\n        avatar.startsWith(\"/\") ||\n        avatar.includes(\".\")\n      );\n    },\n\n    // 判断是否为预设图标\n    isPresetIcon(avatar) {\n      if (!avatar) return false;\n      return (\n        avatar.startsWith(\"fas \") ||\n        avatar.startsWith(\"far \") ||\n        avatar.startsWith(\"fab \")\n      );\n    },\n\n    // 获取图标背景色\n    getIconBgColor(iconClass) {\n      const iconColorMap = {\n        \"fas fa-graduation-cap\": \"#3B82F6\",\n        \"fas fa-book\": \"#10B981\",\n        \"fas fa-pencil-alt\": \"#F59E0B\",\n        \"fas fa-clipboard-check\": \"#EF4444\",\n        \"fas fa-certificate\": \"#8B5CF6\",\n        \"fas fa-trophy\": \"#FBBF24\",\n        \"fas fa-user-md\": \"#DC2626\",\n        \"fas fa-balance-scale\": \"#374151\",\n        \"fas fa-chalkboard-teacher\": \"#059669\",\n        \"fas fa-code\": \"#6366F1\",\n        \"fas fa-calculator\": \"#F97316\",\n        \"fas fa-hard-hat\": \"#92400E\",\n        \"fas fa-users\": \"#06B6D4\",\n        \"fas fa-star\": \"#FBBF24\",\n        \"fas fa-lightbulb\": \"#84CC16\",\n        \"fas fa-fire\": \"#DC2626\",\n        \"fas fa-rocket\": \"#8B5CF6\",\n        \"fas fa-heart\": \"#F87171\",\n      };\n      return iconColorMap[iconClass] || \"#06B6D4\";\n    },\n\n    // 显示选择器\n    showSelector() {\n      this.tempAvatar = this.currentAvatar;\n      this.selectorVisible = true;\n    },\n\n    // 关闭选择器\n    closeSelector() {\n      this.selectorVisible = false;\n      this.tempAvatar = null;\n    },\n\n    // 切换Tab\n    switchTab(index) {\n      this.activeTab = index;\n    },\n\n    // 选择预设头像\n    selectPreset(avatar) {\n      this.tempAvatar = avatar.icon;\n    },\n\n    // 选择图片\n    chooseImage() {\n      uni.chooseImage({\n        count: 1,\n        sizeType: [\"compressed\"],\n        sourceType: [\"album\", \"camera\"],\n        success: (res) => {\n          const tempFilePath = res.tempFilePaths[0];\n          this.tempAvatar = tempFilePath;\n        },\n        fail: () => {\n          uni.showToast({\n            title: \"选择图片失败\",\n            icon: \"none\",\n          });\n        },\n      });\n    },\n\n    // 检查是否选中\n    isSelected(value) {\n      // 优先检查tempAvatar（用户正在选择的），如果没有则检查currentAvatar（当前的）\n      const avatar = this.tempAvatar || this.currentAvatar;\n      return avatar === value;\n    },\n\n    // 确认选择\n    confirmSelection() {\n      if (this.tempAvatar) {\n        this.currentAvatar = this.tempAvatar;\n        this.$emit(\"input\", this.currentAvatar);\n        this.$emit(\"change\", this.currentAvatar);\n      }\n      this.closeSelector();\n    },\n  },\n  mounted() {\n    // 如果没有初始头像，使用默认预设头像\n    if (!this.currentAvatar) {\n      this.currentAvatar = \"fas fa-users\";\n      this.$emit(\"input\", this.currentAvatar);\n    }\n  },\n};\n</script>\n", "import Component from 'E:/work/code/edu/edu-personal-uniapp/components/AvatarSelector.vue'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;AA6KA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA;AAAA,IAEL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA;AAAA,IACV;AAAA;AAAA,IAED,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,EACF;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,eAAe,KAAK,SAAS;AAAA,MAC7B,YAAY;AAAA;AAAA,MACZ,iBAAiB;AAAA;AAAA,MACjB,WAAW;AAAA,MACX,MAAM;AAAA,QACJ,EAAE,MAAM,MAAM,MAAM,eAAgB;AAAA,QACpC,EAAE,MAAM,MAAM,MAAM,gBAAiB;AAAA,MACtC;AAAA;AAAA,MAED,eAAe;AAAA,QACb;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,YACL,EAAE,IAAI,GAAG,MAAM,yBAAyB,SAAS,UAAW;AAAA;AAAA,YAC5D,EAAE,IAAI,GAAG,MAAM,eAAe,SAAS,UAAW;AAAA;AAAA,YAClD,EAAE,IAAI,GAAG,MAAM,qBAAqB,SAAS,UAAW;AAAA;AAAA,YACxD,EAAE,IAAI,GAAG,MAAM,0BAA0B,SAAS,UAAW;AAAA;AAAA,YAC7D,EAAE,IAAI,GAAG,MAAM,sBAAsB,SAAS,UAAW;AAAA;AAAA,YACzD,EAAE,IAAI,GAAG,MAAM,iBAAiB,SAAS,UAAW;AAAA;AAAA,UACrD;AAAA,QACF;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,YACL,EAAE,IAAI,GAAG,MAAM,kBAAkB,SAAS,UAAW;AAAA;AAAA,YACrD,EAAE,IAAI,GAAG,MAAM,wBAAwB,SAAS,UAAW;AAAA;AAAA,YAC3D,EAAE,IAAI,GAAG,MAAM,6BAA6B,SAAS,UAAW;AAAA;AAAA,YAChE,EAAE,IAAI,IAAI,MAAM,eAAe,SAAS,UAAW;AAAA;AAAA,YACnD,EAAE,IAAI,IAAI,MAAM,qBAAqB,SAAS,UAAW;AAAA;AAAA,YACzD,EAAE,IAAI,IAAI,MAAM,mBAAmB,SAAS,UAAW;AAAA;AAAA,UACxD;AAAA,QACF;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,OAAO;AAAA,YACL,EAAE,IAAI,IAAI,MAAM,gBAAgB,SAAS,UAAW;AAAA;AAAA,YACpD,EAAE,IAAI,IAAI,MAAM,eAAe,SAAS,UAAW;AAAA;AAAA,YACnD,EAAE,IAAI,IAAI,MAAM,oBAAoB,SAAS,UAAW;AAAA;AAAA,YACxD,EAAE,IAAI,IAAI,MAAM,eAAe,SAAS,UAAW;AAAA;AAAA,YACnD,EAAE,IAAI,IAAI,MAAM,iBAAiB,SAAS,UAAW;AAAA;AAAA,YACrD,EAAE,IAAI,IAAI,MAAM,gBAAgB,SAAS,UAAW;AAAA;AAAA,UACrD;AAAA,QACF;AAAA,MACF;AAAA;EAEJ;AAAA,EACD,OAAO;AAAA,IACL,OAAO;AAAA,MACL,QAAQ,QAAQ;AACd,aAAK,gBAAgB,UAAU;AAAA,MAChC;AAAA,MACD,WAAW;AAAA,IACZ;AAAA,EACF;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,cAAc,QAAQ;AACpB,UAAI,CAAC;AAAQ,eAAO;AACpB,aACE,OAAO,WAAW,MAAM,KACxB,OAAO,WAAW,OAAO,KACzB,OAAO,WAAW,GAAG,KACrB,OAAO,SAAS,GAAG;AAAA,IAEtB;AAAA;AAAA,IAGD,aAAa,QAAQ;AACnB,UAAI,CAAC;AAAQ,eAAO;AACpB,aACE,OAAO,WAAW,MAAM,KACxB,OAAO,WAAW,MAAM,KACxB,OAAO,WAAW,MAAM;AAAA,IAE3B;AAAA;AAAA,IAGD,eAAe,WAAW;AACxB,YAAM,eAAe;AAAA,QACnB,yBAAyB;AAAA,QACzB,eAAe;AAAA,QACf,qBAAqB;AAAA,QACrB,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,QAClB,wBAAwB;AAAA,QACxB,6BAA6B;AAAA,QAC7B,eAAe;AAAA,QACf,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,QAChB,eAAe;AAAA,QACf,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,iBAAiB;AAAA,QACjB,gBAAgB;AAAA;AAElB,aAAO,aAAa,SAAS,KAAK;AAAA,IACnC;AAAA;AAAA,IAGD,eAAe;AACb,WAAK,aAAa,KAAK;AACvB,WAAK,kBAAkB;AAAA,IACxB;AAAA;AAAA,IAGD,gBAAgB;AACd,WAAK,kBAAkB;AACvB,WAAK,aAAa;AAAA,IACnB;AAAA;AAAA,IAGD,UAAU,OAAO;AACf,WAAK,YAAY;AAAA,IAClB;AAAA;AAAA,IAGD,aAAa,QAAQ;AACnB,WAAK,aAAa,OAAO;AAAA,IAC1B;AAAA;AAAA,IAGD,cAAc;AACZA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AAChB,gBAAM,eAAe,IAAI,cAAc,CAAC;AACxC,eAAK,aAAa;AAAA,QACnB;AAAA,QACD,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACF;AAAA,MACH,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,WAAW,OAAO;AAEhB,YAAM,SAAS,KAAK,cAAc,KAAK;AACvC,aAAO,WAAW;AAAA,IACnB;AAAA;AAAA,IAGD,mBAAmB;AACjB,UAAI,KAAK,YAAY;AACnB,aAAK,gBAAgB,KAAK;AAC1B,aAAK,MAAM,SAAS,KAAK,aAAa;AACtC,aAAK,MAAM,UAAU,KAAK,aAAa;AAAA,MACzC;AACA,WAAK,cAAa;AAAA,IACnB;AAAA,EACF;AAAA,EACD,UAAU;AAER,QAAI,CAAC,KAAK,eAAe;AACvB,WAAK,gBAAgB;AACrB,WAAK,MAAM,SAAS,KAAK,aAAa;AAAA,IACxC;AAAA,EACD;AACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClWA,GAAG,gBAAgB,SAAS;"}