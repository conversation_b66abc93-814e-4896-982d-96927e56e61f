<template>
  <view class="container">
    <view class="content">
      <text class="title">头像选择器 - 字符串格式测试</text>
      
      <!-- 测试区域 -->
      <view class="test-section">
        <!-- 基本测试 -->
        <view class="test-item">
          <text class="test-title">基本功能测试</text>
          <AvatarSelector 
            v-model="avatar1" 
            text="测试群组"
            @change="onAvatarChange1"
          />
          <text class="test-result">当前头像：{{ avatar1 }}</text>
        </view>

        <!-- 预设图标测试 -->
        <view class="test-item">
          <text class="test-title">预设图标测试</text>
          <AvatarSelector 
            v-model="avatar2" 
            text="Vue学习群"
            @change="onAvatarChange2"
          />
          <text class="test-result">当前头像：{{ avatar2 }}</text>
        </view>

        <!-- 自定义图片测试 -->
        <view class="test-item">
          <text class="test-title">自定义图片测试</text>
          <AvatarSelector 
            v-model="avatar3" 
            text="前端开发群"
            @change="onAvatarChange3"
          />
          <text class="test-result">当前头像：{{ avatar3 }}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="button-section">
        <button 
          @click="resetAvatars"
          class="reset-btn"
        >
          重置所有头像
        </button>
        <button 
          @click="showAllData"
          class="show-btn"
        >
          显示所有头像数据
        </button>
        <button 
          @click="testDatabaseFormat"
          class="test-btn"
        >
          测试数据库格式
        </button>
      </view>

      <!-- 数据库格式示例 -->
      <view class="database-example">
        <text class="example-title">数据库存储示例：</text>
        <view class="example-content">
          <text class="example-text">用户表字段：avatar VARCHAR(255)</text>
          <text class="example-text">存储值示例：</text>
          <text class="example-value">• "fas fa-users" (预设图标)</text>
          <text class="example-value">• "https://example.com/avatar.jpg" (自定义图片)</text>
          <text class="example-value">• "/static/uploads/avatar123.png" (本地图片)</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import AvatarSelector from '@/components/AvatarSelector.vue';

export default {
  components: {
    AvatarSelector
  },
  data() {
    return {
      avatar1: 'fas fa-users', // 默认群组图标
      avatar2: 'fas fa-graduation-cap', // 毕业帽图标
      avatar3: '' // 空值，将显示默认头像
    };
  },
  methods: {
    onAvatarChange1(avatar) {
      console.log('头像1变化:', avatar);
      this.avatar1 = avatar;
    },
    onAvatarChange2(avatar) {
      console.log('头像2变化:', avatar);
      this.avatar2 = avatar;
    },
    onAvatarChange3(avatar) {
      console.log('头像3变化:', avatar);
      this.avatar3 = avatar;
    },
    resetAvatars() {
      this.avatar1 = 'fas fa-users';
      this.avatar2 = 'fas fa-graduation-cap';
      this.avatar3 = '';
      uni.showToast({
        title: '头像已重置',
        icon: 'success'
      });
    },
    showAllData() {
      const data = {
        avatar1: this.avatar1,
        avatar2: this.avatar2,
        avatar3: this.avatar3
      };
      console.log('所有头像数据:', data);
      uni.showModal({
        title: '头像数据',
        content: JSON.stringify(data, null, 2),
        showCancel: false
      });
    },
    testDatabaseFormat() {
      // 模拟数据库存储和读取
      const mockDatabase = {
        users: [
          { id: 1, name: '张三', avatar: 'fas fa-users' },
          { id: 2, name: '李四', avatar: 'fas fa-graduation-cap' },
          { id: 3, name: '王五', avatar: 'https://example.com/avatar.jpg' }
        ]
      };
      
      console.log('模拟数据库数据:', mockDatabase);
      uni.showModal({
        title: '数据库格式测试',
        content: `用户数据：\n${JSON.stringify(mockDatabase.users, null, 2)}`,
        showCancel: false
      });
    }
  }
};
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content {
  padding: 20px;
}

.title {
  font-size: 20px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}

.test-section {
  margin-bottom: 30px;
}

.test-item {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  display: block;
}

.test-result {
  font-size: 14px;
  color: #666;
  margin-top: 10px;
  display: block;
  word-break: break-all;
}

.button-section {
  margin-bottom: 30px;
}

.reset-btn, .show-btn, .test-btn {
  width: 100%;
  padding: 12px;
  margin-bottom: 10px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  border: none;
}

.reset-btn {
  background-color: #6b7280;
  color: white;
}

.show-btn {
  background-color: #3b82f6;
  color: white;
}

.test-btn {
  background-color: #10b981;
  color: white;
}

.database-example {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.example-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  display: block;
}

.example-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.example-text {
  font-size: 14px;
  color: #666;
}

.example-value {
  font-size: 14px;
  color: #059669;
  font-family: monospace;
  padding-left: 10px;
}
</style>
