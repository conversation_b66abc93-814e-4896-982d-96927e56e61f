"use strict";
const common_vendor = require("../../common/vendor.js");
const AvatarSelector = () => "../../components/AvatarSelector.js";
const _sfc_main = {
  components: {
    AvatarSelector
  },
  data() {
    return {
      navBarStyle: {},
      navBarButtonStyle: {},
      submitting: false,
      form: {
        name: "",
        avatar: "fas fa-users",
        // 默认头像为群组图标
        description: "",
        notice: "",
        joinType: 1,
        password: ""
      },
      errors: {},
      joinOptions: [
        { value: 1, label: "不限制", desc: "任何人都可以直接加入群组" },
        { value: 2, label: "审核后加入", desc: "需要群主或管理员审核通过" },
        { value: 3, label: "口令加入", desc: "需要输入正确的6位数字口令" }
      ]
    };
  },
  methods: {
    setNavBarButtonStyle() {
      let style = {};
      const menuButton = common_vendor.index.getMenuButtonBoundingClientRect();
      style = {
        position: "absolute",
        left: "16px",
        top: menuButton.top + "px",
        width: menuButton.height + "px",
        height: menuButton.height + "px",
        "z-index": 20
      };
      this.navBarButtonStyle = style;
    },
    goBack() {
      common_vendor.index.navigateBack();
    },
    onAvatarChange(avatar) {
      this.form.avatar = avatar;
      common_vendor.index.__f__("log", "at pages/group/create.vue:232", "头像已更新:", avatar);
    },
    validateForm() {
      this.errors = {};
      if (!this.form.name.trim()) {
        this.errors.name = "请输入群名称";
        return false;
      }
      if (this.form.name.length > 16) {
        this.errors.name = "群名称不能超过16个字符";
        return false;
      }
      if (!this.form.description.trim()) {
        this.errors.description = "请输入群介绍";
        return false;
      }
      if (this.form.description.length > 80) {
        this.errors.description = "群介绍不能超过80个字符";
        return false;
      }
      if (this.form.joinType === 3) {
        if (!this.form.password || this.form.password.length !== 6) {
          this.errors.password = "请输入6位数字口令";
          return false;
        }
        if (!/^\d{6}$/.test(this.form.password)) {
          this.errors.password = "口令必须是6位数字";
          return false;
        }
      }
      return true;
    },
    async submitForm() {
      if (!this.validateForm()) {
        return;
      }
      this.submitting = true;
      try {
        common_vendor.index.showToast({
          title: "创建成功",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      } catch (error) {
        common_vendor.index.showToast({
          title: error.message || "创建失败",
          icon: "none"
        });
      } finally {
        this.submitting = false;
      }
    }
  },
  onLoad() {
  }
};
if (!Array) {
  const _component_AvatarSelector = common_vendor.resolveComponent("AvatarSelector");
  _component_AvatarSelector();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.s($data.navBarStyle),
    b: common_vendor.o($options.onAvatarChange),
    c: common_vendor.o(($event) => $data.form.avatar = $event),
    d: common_vendor.p({
      text: $data.form.name,
      modelValue: $data.form.avatar
    }),
    e: $data.form.name,
    f: common_vendor.o(($event) => $data.form.name = $event.detail.value),
    g: $data.errors.name
  }, $data.errors.name ? {
    h: common_vendor.t($data.errors.name)
  } : {}, {
    i: common_vendor.t($data.form.name.length),
    j: $data.form.description,
    k: common_vendor.o(($event) => $data.form.description = $event.detail.value),
    l: $data.errors.description
  }, $data.errors.description ? {
    m: common_vendor.t($data.errors.description)
  } : {}, {
    n: common_vendor.t($data.form.description.length),
    o: $data.form.notice,
    p: common_vendor.o(($event) => $data.form.notice = $event.detail.value),
    q: common_vendor.f($data.joinOptions, (option, k0, i0) => {
      return common_vendor.e({
        a: $data.form.joinType === option.value
      }, $data.form.joinType === option.value ? {} : {}, {
        b: common_vendor.n($data.form.joinType === option.value ? "border-primary-500" : "border-gray-300"),
        c: common_vendor.t(option.label),
        d: common_vendor.t(option.desc),
        e: option.value,
        f: common_vendor.n($data.form.joinType === option.value ? "border-primary-500 bg-primary-50" : ""),
        g: common_vendor.o(($event) => $data.form.joinType = option.value, option.value)
      });
    }),
    r: $data.form.joinType === 3
  }, $data.form.joinType === 3 ? common_vendor.e({
    s: $data.form.password,
    t: common_vendor.o(($event) => $data.form.password = $event.detail.value),
    v: $data.errors.password
  }, $data.errors.password ? {
    w: common_vendor.t($data.errors.password)
  } : {}) : {}, {
    x: $data.submitting
  }, $data.submitting ? {} : {}, {
    y: common_vendor.o((...args) => $options.submitForm && $options.submitForm(...args)),
    z: $data.submitting
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/group/create.js.map
