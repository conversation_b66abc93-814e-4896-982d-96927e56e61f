"use strict";
const common_vendor = require("../../common/vendor.js");
const AvatarSelector = () => "../../components/AvatarSelector.js";
const _sfc_main = {
  components: {
    AvatarSelector
  },
  data() {
    return {
      dynamicText: "前端学习群",
      avatar1: "fas fa-users",
      // 默认群组图标
      avatar2: "fas fa-book",
      // 书本图标
      avatar3: "fas fa-graduation-cap"
      // 毕业帽图标
    };
  },
  methods: {
    onAvatarChange1(avatar) {
      common_vendor.index.__f__("log", "at pages/test/avatar-test.vue:97", "头像1变化:", avatar);
      this.avatar1 = avatar;
    },
    onAvatarChange2(avatar) {
      common_vendor.index.__f__("log", "at pages/test/avatar-test.vue:101", "头像2变化:", avatar);
      this.avatar2 = avatar;
    },
    onAvatarChange3(avatar) {
      common_vendor.index.__f__("log", "at pages/test/avatar-test.vue:105", "头像3变化:", avatar);
      this.avatar3 = avatar;
    },
    resetAvatars() {
      this.avatar1 = "fas fa-users";
      this.avatar2 = "fas fa-book";
      this.avatar3 = "fas fa-graduation-cap";
      common_vendor.index.showToast({
        title: "头像已重置",
        icon: "success"
      });
    },
    showAllData() {
      const data = {
        avatar1: this.avatar1,
        avatar2: this.avatar2,
        avatar3: this.avatar3,
        dynamicText: this.dynamicText
      };
      common_vendor.index.__f__("log", "at pages/test/avatar-test.vue:124", "所有头像数据:", data);
      common_vendor.index.showModal({
        title: "头像数据",
        content: JSON.stringify(data, null, 2),
        showCancel: false
      });
    }
  }
};
if (!Array) {
  const _component_AvatarSelector = common_vendor.resolveComponent("AvatarSelector");
  _component_AvatarSelector();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o($options.onAvatarChange1),
    b: common_vendor.o(($event) => $data.avatar1 = $event),
    c: common_vendor.p({
      text: "测试群组",
      modelValue: $data.avatar1
    }),
    d: common_vendor.t(JSON.stringify($data.avatar1)),
    e: $data.dynamicText,
    f: common_vendor.o(($event) => $data.dynamicText = $event.detail.value),
    g: common_vendor.o($options.onAvatarChange2),
    h: common_vendor.o(($event) => $data.avatar2 = $event),
    i: common_vendor.p({
      text: $data.dynamicText,
      modelValue: $data.avatar2
    }),
    j: common_vendor.t(JSON.stringify($data.avatar2)),
    k: common_vendor.o($options.onAvatarChange3),
    l: common_vendor.o(($event) => $data.avatar3 = $event),
    m: common_vendor.p({
      text: "Vue学习群",
      modelValue: $data.avatar3
    }),
    n: common_vendor.t(JSON.stringify($data.avatar3)),
    o: common_vendor.o((...args) => $options.resetAvatars && $options.resetAvatars(...args)),
    p: common_vendor.o((...args) => $options.showAllData && $options.showAllData(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-7e7644d8"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/test/avatar-test.js.map
