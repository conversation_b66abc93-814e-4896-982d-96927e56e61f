<template>
  <view class="h-screen bg-gray-50 flex flex-col">
    <!-- 自定义导航栏 -->
    <view class="relative w-full mb-4">
      <view
        class="w-full bg-gradient-to-r bg-primary-500 to-primary-600 relative overflow-hidden"
        :style="navBarStyle"
      >
        <!-- 导航内容 -->
        <view class="px-4 py-6 relative z-10">
          <view class="text-center">
            <view class="flex items-center justify-center mb-2">
              <view
                class="w-10 h-10 rounded-full bg-white bg-opacity-20 flex items-center justify-center mr-3 backdrop-blur-sm"
              >
                <i class="fas fa-plus text-white text-lg"></i>
              </view>
              <text class="text-white text-xl font-bold">创建群组</text>
            </view>
            <text class="text-white text-sm opacity-90"
              >填写群组信息，创建专属学习空间</text
            >
          </view>
        </view>
      </view>
    </view>

    <!-- 表单内容 -->
    <view class="flex-1 px-4 pb-6">
      <view class="bg-white rounded-xl shadow-sm p-5">
        <!-- 群头像 -->
        <view class="mb-6">
          <text class="text-base font-semibold text-gray-800 mb-3 block"
            >群头像</text
          >
          <AvatarSelector
            v-model="form.avatar"
            :text="form.name"
            @change="onAvatarChange"
          />
        </view>

        <!-- 群名称 -->
        <view class="mb-6">
          <view class="flex items-center mb-3">
            <text class="text-base font-semibold text-gray-800">群名称</text>
            <text class="text-red-500 ml-1">*</text>
          </view>
          <input
            v-model="form.name"
            placeholder="请输入群名称（16字以内）"
            maxlength="16"
            class="w-full h-auto px-4 py-3 border border-gray-200 rounded-lg text-base"
          />
          <view class="flex justify-between mt-1">
            <text class="text-xs text-red-500" v-if="errors.name">{{
              errors.name
            }}</text>
            <text class="text-xs text-gray-400 ml-auto"
              >{{ form.name.length }}/16</text
            >
          </view>
        </view>

        <!-- 群介绍 -->
        <view class="mb-6">
          <view class="flex items-center mb-3">
            <text class="text-base font-semibold text-gray-800">群介绍</text>
            <text class="text-red-500 ml-1">*</text>
          </view>
          <textarea
            v-model="form.description"
            placeholder="请输入群介绍（80字以内）"
            maxlength="80"
            class="w-full px-4 py-3 border border-gray-200 rounded-lg text-base h-20"
          />
          <view class="flex justify-between mt-1">
            <text class="text-xs text-red-500" v-if="errors.description">{{
              errors.description
            }}</text>
            <text class="text-xs text-gray-400 ml-auto"
              >{{ form.description.length }}/80</text
            >
          </view>
        </view>

        <!-- 群公告 -->
        <view class="mb-6">
          <text class="text-base font-semibold text-gray-800 mb-3 block"
            >群公告</text
          >
          <textarea
            v-model="form.notice"
            placeholder="请输入群公告（选填）"
            class="w-full px-4 py-3 border border-gray-200 rounded-lg text-base h-20"
          />
        </view>

        <!-- 加入方式 -->
        <view class="mb-6">
          <view class="flex items-center mb-3">
            <text class="text-base font-semibold text-gray-800">加入方式</text>
            <text class="text-red-500 ml-1">*</text>
          </view>
          <view class="space-y-3">
            <view
              v-for="option in joinOptions"
              :key="option.value"
              class="flex items-center p-3 border border-gray-200 rounded-lg"
              :class="
                form.joinType === option.value
                  ? 'border-primary-500 bg-primary-50'
                  : ''
              "
              @click="form.joinType = option.value"
            >
              <view
                class="w-5 h-5 rounded-full border-2 flex items-center justify-center mr-3"
                :class="
                  form.joinType === option.value
                    ? 'border-primary-500'
                    : 'border-gray-300'
                "
              >
                <view
                  v-if="form.joinType === option.value"
                  class="w-2.5 h-2.5 rounded-full bg-primary-500"
                ></view>
              </view>
              <view class="flex-1">
                <text class="text-base font-medium text-gray-800 block">{{
                  option.label
                }}</text>
                <text class="text-sm text-gray-500">{{ option.desc }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 口令设置 -->
        <view v-if="form.joinType === 3" class="mb-6">
          <text class="text-base font-semibold text-gray-800 mb-3 block"
            >入群口令</text
          >
          <input
            v-model="form.password"
            placeholder="请输入6位数字口令"
            maxlength="6"
            type="number"
            class="h-auto w-full px-4 py-3 border border-gray-200 rounded-lg text-base"
          />
          <text class="text-xs text-red-500 mt-1" v-if="errors.password">{{
            errors.password
          }}</text>
        </view>

        <!-- 创建按钮 -->
        <view class="space-y-3">
          <button
            class="w-full bg-primary-500 text-white py-3 rounded-lg text-base font-medium"
            @click="submitForm"
            :disabled="submitting"
          >
            <text v-if="submitting">创建中...</text>
            <text v-else>创建群组</text>
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import AvatarSelector from "@/components/AvatarSelector.vue";

export default {
  components: {
    AvatarSelector,
  },
  data() {
    return {
      navBarStyle: {},
      navBarButtonStyle: {},
      submitting: false,
      form: {
        name: "",
        avatar: "fas fa-users", // 默认头像为群组图标
        description: "",
        notice: "",
        joinType: 1,
        password: "",
      },
      errors: {},
      joinOptions: [
        { value: 1, label: "不限制", desc: "任何人都可以直接加入群组" },
        { value: 2, label: "审核后加入", desc: "需要群主或管理员审核通过" },
        { value: 3, label: "口令加入", desc: "需要输入正确的6位数字口令" },
      ],
    };
  },
  methods: {
    setNavBarButtonStyle() {
      let style = {};
      // #ifdef MP-WEIXIN
      const menuButton = uni.getMenuButtonBoundingClientRect();
      style = {
        position: "absolute",
        left: "16px",
        top: menuButton.top + "px",
        width: menuButton.height + "px",
        height: menuButton.height + "px",
        "z-index": 20,
      };
      // #endif
      // #ifdef H5
      style = {
        position: "absolute",
        left: "16px",
        top: "16px",
        width: "40px",
        height: "40px",
        "z-index": 20,
      };
      // #endif
      this.navBarButtonStyle = style;
    },
    goBack() {
      uni.navigateBack();
    },
    onAvatarChange(avatar) {
      this.form.avatar = avatar;
      console.log("头像已更新:", avatar);
    },
    validateForm() {
      this.errors = {};

      if (!this.form.name.trim()) {
        this.errors.name = "请输入群名称";
        return false;
      }
      if (this.form.name.length > 16) {
        this.errors.name = "群名称不能超过16个字符";
        return false;
      }

      if (!this.form.description.trim()) {
        this.errors.description = "请输入群介绍";
        return false;
      }
      if (this.form.description.length > 80) {
        this.errors.description = "群介绍不能超过80个字符";
        return false;
      }

      if (this.form.joinType === 3) {
        if (!this.form.password || this.form.password.length !== 6) {
          this.errors.password = "请输入6位数字口令";
          return false;
        }
        if (!/^\d{6}$/.test(this.form.password)) {
          this.errors.password = "口令必须是6位数字";
          return false;
        }
      }

      return true;
    },
    async submitForm() {
      if (!this.validateForm()) {
        return;
      }

      this.submitting = true;

      try {
        // TODO: 调用创建群组接口
        // const res = await this.$reqPost('/front/group/create', this.form);

        uni.showToast({
          title: "创建成功",
          icon: "success",
        });

        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      } catch (error) {
        uni.showToast({
          title: error.message || "创建失败",
          icon: "none",
        });
      } finally {
        this.submitting = false;
      }
    },
  },
  onLoad() {},
};
</script>

<style scoped></style>
