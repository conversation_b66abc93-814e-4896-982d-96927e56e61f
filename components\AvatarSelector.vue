<template>
  <view class="flex flex-col items-center">
    <!-- 头像显示区域 -->
    <view
      class="flex flex-col items-center cursor-pointer"
      @click="showSelector"
    >
      <view class="relative w-20 h-20 mb-2">
        <!-- 自定义上传的头像 -->
        <image
          v-if="isCustomImage(currentAvatar)"
          :src="currentAvatar"
          class="w-full h-full rounded-xl object-cover"
          mode="aspectFill"
        />
        <!-- 预设图标头像 -->
        <view
          v-else-if="isPresetIcon(currentAvatar)"
          class="w-full h-full rounded-xl flex items-center justify-center"
          :style="{ backgroundColor: getIconBgColor(currentAvatar) }"
        >
          <i :class="currentAvatar" class="text-white text-3xl"></i>
        </view>
        <!-- 默认头像 -->
        <view
          v-else
          class="w-full h-full rounded-xl flex items-center justify-center bg-gray-200"
        >
          <i class="fas fa-user text-gray-400 text-3xl"></i>
        </view>

        <!-- 编辑图标 -->
        <view
          class="absolute -bottom-0.5 -right-0.5 w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center border-2 border-white"
        >
          <i class="fas fa-camera text-white text-xs"></i>
        </view>
      </view>
      <text class="text-xs text-gray-500">点击更换头像</text>
    </view>

    <!-- 头像选择弹窗 -->
    <view
      v-if="selectorVisible"
      class="fixed inset-0 z-50 flex items-end"
      @click="closeSelector"
    >
      <view class="absolute inset-0 bg-black bg-opacity-50"></view>
      <view
        class="bg-white rounded-t-2xl w-full flex flex-col relative z-10"
        style="max-height: 75vh"
        @click.stop
      >
        <!-- 弹窗标题 -->
        <view
          class="flex justify-between items-center p-4 border-b border-gray-100"
        >
          <text class="text-lg font-semibold text-gray-800">选择头像</text>
          <view
            class="w-8 h-8 flex items-center justify-center cursor-pointer"
            @click="closeSelector"
          >
            <i class="fas fa-times text-gray-500"></i>
          </view>
        </view>

        <!-- Tab切换 -->
        <view class="flex border-b border-gray-100">
          <view
            v-for="(tab, index) in tabs"
            :key="index"
            class="flex-1 flex flex-col items-center py-3 cursor-pointer transition-colors"
            :class="
              activeTab === index
                ? 'text-primary-500 border-b-2 border-primary-500'
                : 'text-gray-500'
            "
            @click="switchTab(index)"
          >
            <i :class="tab.icon" class="text-base mb-1"></i>
            <text class="text-xs">{{ tab.name }}</text>
          </view>
        </view>

        <!-- 内容区域 -->
        <scroll-view
          class="flex-1 p-4"
          scroll-y
          style="max-height: calc(75vh - 140px)"
        >
          <!-- 预设头像 -->
          <view v-if="activeTab === 0" class="space-y-4">
            <view
              v-for="category in presetAvatars"
              :key="category.name"
              class="mb-4"
            >
              <text class="text-sm font-semibold text-gray-700 mb-2 block">{{
                category.name
              }}</text>
              <view class="grid grid-cols-6 gap-2">
                <view
                  v-for="avatar in category.items"
                  :key="avatar.id"
                  class="flex justify-center cursor-pointer"
                  @click="selectPreset(avatar)"
                >
                  <view
                    class="w-9 h-9 rounded-lg flex items-center justify-center transition-all relative"
                    :class="
                      isSelected(avatar.icon)
                        ? 'ring-2 ring-primary-500 ring-offset-1'
                        : ''
                    "
                    :style="{ backgroundColor: avatar.bgColor }"
                  >
                    <i :class="avatar.icon" class="text-white text-sm"></i>
                    <!-- 选中标识 -->
                    <view
                      v-if="isSelected(avatar.icon)"
                      class="absolute -top-1 -right-1 w-4 h-4 bg-primary-500 rounded-full flex items-center justify-center"
                    >
                      <i class="fas fa-check text-white text-xs"></i>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 自定义上传 -->
          <view v-if="activeTab === 1" class="flex justify-center py-10">
            <view @click="chooseImage">
              <view
                class="flex flex-col items-center p-10 border-2 border-dashed border-gray-300 rounded-xl cursor-pointer hover:border-blue-400 hover:bg-gray-50 transition-colors"
              >
                <i
                  class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-3"
                ></i>
                <text class="text-base text-gray-700 mb-2">点击上传图片</text>
                <text class="text-xs text-gray-500 text-center"
                  >支持 JPG、PNG 格式，建议尺寸 200x200</text
                >
              </view>
            </view>
          </view>
        </scroll-view>

        <!-- 底部按钮 -->
        <view class="flex gap-3 p-4 border-t border-gray-100">
          <view class="flex-1">
            <button
              class="w-full py-3 px-4 bg-gray-100 text-base text-gray-700 rounded-lg font-medium"
              @click="closeSelector"
            >
              取消
            </button>
          </view>
          <view class="flex-1">
            <button
              class="w-full py-3 px-4 bg-primary-500 text-base text-white rounded-lg font-medium"
              @click="confirmSelection"
            >
              确定
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "AvatarSelector",
  props: {
    // 头像值（字符串格式：图标类名或图片URL）
    value: {
      type: String,
      default: "fas fa-users", // 默认图标
    },
    // 用于生成文字头像的文本（如群组名称）
    text: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      currentAvatar: this.value || "fas fa-users",
      tempAvatar: null, // 临时选择的头像（字符串）
      selectorVisible: false, // 控制弹窗显示
      activeTab: 0,
      tabs: [
        { name: "图标", icon: "fas fa-icons" },
        { name: "上传", icon: "fas fa-upload" },
      ],
      // 预设头像分类
      presetAvatars: [
        {
          name: "学习考试",
          items: [
            { id: 1, icon: "fas fa-graduation-cap", bgColor: "#3B82F6" }, // 毕业帽
            { id: 2, icon: "fas fa-book", bgColor: "#10B981" }, // 书本
            { id: 3, icon: "fas fa-pencil-alt", bgColor: "#F59E0B" }, // 铅笔
            { id: 4, icon: "fas fa-clipboard-check", bgColor: "#EF4444" }, // 考试
            { id: 5, icon: "fas fa-certificate", bgColor: "#8B5CF6" }, // 证书
            { id: 6, icon: "fas fa-trophy", bgColor: "#FBBF24" }, // 奖杯
          ],
        },
        {
          name: "各行各业",
          items: [
            { id: 7, icon: "fas fa-user-md", bgColor: "#DC2626" }, // 医生
            { id: 8, icon: "fas fa-balance-scale", bgColor: "#374151" }, // 法律
            { id: 9, icon: "fas fa-chalkboard-teacher", bgColor: "#059669" }, // 教师
            { id: 10, icon: "fas fa-code", bgColor: "#6366F1" }, // 程序员
            { id: 11, icon: "fas fa-calculator", bgColor: "#F97316" }, // 会计
            { id: 12, icon: "fas fa-hard-hat", bgColor: "#92400E" }, // 工程师
          ],
        },
        {
          name: "通用图标",
          items: [
            { id: 13, icon: "fas fa-users", bgColor: "#06B6D4" }, // 群组
            { id: 14, icon: "fas fa-star", bgColor: "#FBBF24" }, // 星星
            { id: 15, icon: "fas fa-lightbulb", bgColor: "#84CC16" }, // 灯泡
            { id: 16, icon: "fas fa-fire", bgColor: "#DC2626" }, // 火焰
            { id: 17, icon: "fas fa-rocket", bgColor: "#8B5CF6" }, // 火箭
            { id: 18, icon: "fas fa-heart", bgColor: "#F87171" }, // 爱心
          ],
        },
      ],
    };
  },
  watch: {
    value: {
      handler(newVal) {
        this.currentAvatar = newVal || "fas fa-users";
      },
      immediate: true,
    },
  },
  methods: {
    // 判断是否为自定义图片
    isCustomImage(avatar) {
      if (!avatar) return false;
      return (
        avatar.startsWith("http") ||
        avatar.startsWith("data:") ||
        avatar.startsWith("/") ||
        avatar.includes(".")
      );
    },

    // 判断是否为预设图标
    isPresetIcon(avatar) {
      if (!avatar) return false;
      return (
        avatar.startsWith("fas ") ||
        avatar.startsWith("far ") ||
        avatar.startsWith("fab ")
      );
    },

    // 获取图标背景色
    getIconBgColor(iconClass) {
      const iconColorMap = {
        "fas fa-graduation-cap": "#3B82F6",
        "fas fa-book": "#10B981",
        "fas fa-pencil-alt": "#F59E0B",
        "fas fa-clipboard-check": "#EF4444",
        "fas fa-certificate": "#8B5CF6",
        "fas fa-trophy": "#FBBF24",
        "fas fa-user-md": "#DC2626",
        "fas fa-balance-scale": "#374151",
        "fas fa-chalkboard-teacher": "#059669",
        "fas fa-code": "#6366F1",
        "fas fa-calculator": "#F97316",
        "fas fa-hard-hat": "#92400E",
        "fas fa-users": "#06B6D4",
        "fas fa-star": "#FBBF24",
        "fas fa-lightbulb": "#84CC16",
        "fas fa-fire": "#DC2626",
        "fas fa-rocket": "#8B5CF6",
        "fas fa-heart": "#F87171",
      };
      return iconColorMap[iconClass] || "#06B6D4";
    },

    // 显示选择器
    showSelector() {
      this.tempAvatar = this.currentAvatar;
      this.selectorVisible = true;
    },

    // 关闭选择器
    closeSelector() {
      this.selectorVisible = false;
      this.tempAvatar = null;
    },

    // 切换Tab
    switchTab(index) {
      this.activeTab = index;
    },

    // 选择预设头像
    selectPreset(avatar) {
      this.tempAvatar = avatar.icon;
    },

    // 选择图片
    chooseImage() {
      uni.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          this.tempAvatar = tempFilePath;
        },
        fail: () => {
          uni.showToast({
            title: "选择图片失败",
            icon: "none",
          });
        },
      });
    },

    // 检查是否选中
    isSelected(value) {
      // 优先检查tempAvatar（用户正在选择的），如果没有则检查currentAvatar（当前的）
      const avatar = this.tempAvatar || this.currentAvatar;
      return avatar === value;
    },

    // 确认选择
    confirmSelection() {
      if (this.tempAvatar) {
        this.currentAvatar = this.tempAvatar;
        this.$emit("input", this.currentAvatar);
        this.$emit("change", this.currentAvatar);
      }
      this.closeSelector();
    },
  },
  mounted() {
    // 如果没有初始头像，使用默认预设头像
    if (!this.currentAvatar) {
      this.currentAvatar = "fas fa-users";
      this.$emit("input", this.currentAvatar);
    }
  },
};
</script>
