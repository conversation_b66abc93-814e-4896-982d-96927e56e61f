<template>
  <view class="min-h-screen bg-gray-50 p-4">
    <view class="bg-white rounded-xl p-6 shadow-sm">
      <text class="text-xl font-bold text-gray-800 mb-6 block"
        >头像选择组件测试</text
      >

      <!-- 测试区域 -->
      <view class="space-y-6">
        <!-- 基本测试 -->
        <view>
          <text class="text-base font-semibold text-gray-700 mb-3 block"
            >基本功能测试</text
          >
          <AvatarSelector
            v-model="avatar1"
            text="测试群组"
            @change="onAvatarChange1"
          />
          <text class="text-sm text-gray-500 mt-2 block"
            >当前头像数据：{{ JSON.stringify(avatar1) }}</text
          >
        </view>

        <!-- 动态文本测试 -->
        <view>
          <text class="text-base font-semibold text-gray-700 mb-3 block"
            >动态文本测试</text
          >
          <input
            v-model="dynamicText"
            placeholder="输入群组名称"
            class="w-full p-3 border border-gray-200 rounded-lg mb-3"
          />
          <AvatarSelector
            v-model="avatar2"
            :text="dynamicText"
            @change="onAvatarChange2"
          />
          <text class="text-sm text-gray-500 mt-2 block"
            >当前头像数据：{{ JSON.stringify(avatar2) }}</text
          >
        </view>

        <!-- 预设头像测试 -->
        <view>
          <text class="text-base font-semibold text-gray-700 mb-3 block"
            >预设头像测试</text
          >
          <AvatarSelector
            v-model="avatar3"
            text="Vue学习群"
            @change="onAvatarChange3"
          />
          <text class="text-sm text-gray-500 mt-2 block"
            >当前头像数据：{{ JSON.stringify(avatar3) }}</text
          >
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="mt-8 space-y-3">
        <button
          @click="resetAvatars"
          class="w-full bg-gray-500 text-white py-3 rounded-lg font-medium"
        >
          重置所有头像
        </button>
        <button
          @click="showAllData"
          class="w-full bg-blue-500 text-white py-3 rounded-lg font-medium"
        >
          显示所有头像数据
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import AvatarSelector from "@/components/AvatarSelector.vue";

export default {
  components: {
    AvatarSelector,
  },
  data() {
    return {
      dynamicText: "前端学习群",
      avatar1: "fas fa-users", // 默认群组图标
      avatar2: "fas fa-book", // 书本图标
      avatar3: "fas fa-graduation-cap", // 毕业帽图标
    };
  },
  methods: {
    onAvatarChange1(avatar) {
      console.log("头像1变化:", avatar);
      this.avatar1 = avatar;
    },
    onAvatarChange2(avatar) {
      console.log("头像2变化:", avatar);
      this.avatar2 = avatar;
    },
    onAvatarChange3(avatar) {
      console.log("头像3变化:", avatar);
      this.avatar3 = avatar;
    },
    resetAvatars() {
      this.avatar1 = "fas fa-users";
      this.avatar2 = "fas fa-book";
      this.avatar3 = "fas fa-graduation-cap";
      uni.showToast({
        title: "头像已重置",
        icon: "success",
      });
    },
    showAllData() {
      const data = {
        avatar1: this.avatar1,
        avatar2: this.avatar2,
        avatar3: this.avatar3,
        dynamicText: this.dynamicText,
      };
      console.log("所有头像数据:", data);
      uni.showModal({
        title: "头像数据",
        content: JSON.stringify(data, null, 2),
        showCancel: false,
      });
    },
  },
};
</script>

<style scoped>
.space-y-6 > view:not(:first-child) {
  margin-top: 1.5rem;
}

.space-y-3 > button:not(:first-child) {
  margin-top: 0.75rem;
}
</style>
