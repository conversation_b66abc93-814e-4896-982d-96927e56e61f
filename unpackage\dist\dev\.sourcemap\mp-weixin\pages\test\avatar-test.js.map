{"version": 3, "file": "avatar-test.js", "sources": ["pages/test/avatar-test.vue", "D:/Program Files/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdGVzdC9hdmF0YXItdGVzdC52dWU"], "sourcesContent": ["<template>\n  <view class=\"min-h-screen bg-gray-50 p-4\">\n    <view class=\"bg-white rounded-xl p-6 shadow-sm\">\n      <text class=\"text-xl font-bold text-gray-800 mb-6 block\"\n        >头像选择组件测试</text\n      >\n\n      <!-- 测试区域 -->\n      <view class=\"space-y-6\">\n        <!-- 基本测试 -->\n        <view>\n          <text class=\"text-base font-semibold text-gray-700 mb-3 block\"\n            >基本功能测试</text\n          >\n          <AvatarSelector\n            v-model=\"avatar1\"\n            text=\"测试群组\"\n            @change=\"onAvatarChange1\"\n          />\n          <text class=\"text-sm text-gray-500 mt-2 block\"\n            >当前头像数据：{{ JSON.stringify(avatar1) }}</text\n          >\n        </view>\n\n        <!-- 动态文本测试 -->\n        <view>\n          <text class=\"text-base font-semibold text-gray-700 mb-3 block\"\n            >动态文本测试</text\n          >\n          <input\n            v-model=\"dynamicText\"\n            placeholder=\"输入群组名称\"\n            class=\"w-full p-3 border border-gray-200 rounded-lg mb-3\"\n          />\n          <AvatarSelector\n            v-model=\"avatar2\"\n            :text=\"dynamicText\"\n            @change=\"onAvatarChange2\"\n          />\n          <text class=\"text-sm text-gray-500 mt-2 block\"\n            >当前头像数据：{{ JSON.stringify(avatar2) }}</text\n          >\n        </view>\n\n        <!-- 预设头像测试 -->\n        <view>\n          <text class=\"text-base font-semibold text-gray-700 mb-3 block\"\n            >预设头像测试</text\n          >\n          <AvatarSelector\n            v-model=\"avatar3\"\n            text=\"Vue学习群\"\n            @change=\"onAvatarChange3\"\n          />\n          <text class=\"text-sm text-gray-500 mt-2 block\"\n            >当前头像数据：{{ JSON.stringify(avatar3) }}</text\n          >\n        </view>\n      </view>\n\n      <!-- 操作按钮 -->\n      <view class=\"mt-8 space-y-3\">\n        <button\n          @click=\"resetAvatars\"\n          class=\"w-full bg-gray-500 text-white py-3 rounded-lg font-medium\"\n        >\n          重置所有头像\n        </button>\n        <button\n          @click=\"showAllData\"\n          class=\"w-full bg-blue-500 text-white py-3 rounded-lg font-medium\"\n        >\n          显示所有头像数据\n        </button>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport AvatarSelector from \"@/components/AvatarSelector.vue\";\n\nexport default {\n  components: {\n    AvatarSelector,\n  },\n  data() {\n    return {\n      dynamicText: \"前端学习群\",\n      avatar1: \"fas fa-users\", // 默认群组图标\n      avatar2: \"fas fa-book\", // 书本图标\n      avatar3: \"fas fa-graduation-cap\", // 毕业帽图标\n    };\n  },\n  methods: {\n    onAvatarChange1(avatar) {\n      console.log(\"头像1变化:\", avatar);\n      this.avatar1 = avatar;\n    },\n    onAvatarChange2(avatar) {\n      console.log(\"头像2变化:\", avatar);\n      this.avatar2 = avatar;\n    },\n    onAvatarChange3(avatar) {\n      console.log(\"头像3变化:\", avatar);\n      this.avatar3 = avatar;\n    },\n    resetAvatars() {\n      this.avatar1 = \"fas fa-users\";\n      this.avatar2 = \"fas fa-book\";\n      this.avatar3 = \"fas fa-graduation-cap\";\n      uni.showToast({\n        title: \"头像已重置\",\n        icon: \"success\",\n      });\n    },\n    showAllData() {\n      const data = {\n        avatar1: this.avatar1,\n        avatar2: this.avatar2,\n        avatar3: this.avatar3,\n        dynamicText: this.dynamicText,\n      };\n      console.log(\"所有头像数据:\", data);\n      uni.showModal({\n        title: \"头像数据\",\n        content: JSON.stringify(data, null, 2),\n        showCancel: false,\n      });\n    },\n  },\n};\n</script>\n\n<style scoped>\n.space-y-6 > view:not(:first-child) {\n  margin-top: 1.5rem;\n}\n\n.space-y-3 > button:not(:first-child) {\n  margin-top: 0.75rem;\n}\n</style>\n", "import MiniProgramPage from 'E:/work/code/edu/edu-personal-uniapp/pages/test/avatar-test.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAgFA,MAAK,iBAAkB,MAAW;AAElC,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,aAAa;AAAA,MACb,SAAS;AAAA;AAAA,MACT,SAAS;AAAA;AAAA,MACT,SAAS;AAAA;AAAA;EAEZ;AAAA,EACD,SAAS;AAAA,IACP,gBAAgB,QAAQ;AACtBA,oBAAY,MAAA,MAAA,OAAA,oCAAA,UAAU,MAAM;AAC5B,WAAK,UAAU;AAAA,IAChB;AAAA,IACD,gBAAgB,QAAQ;AACtBA,oBAAY,MAAA,MAAA,OAAA,qCAAA,UAAU,MAAM;AAC5B,WAAK,UAAU;AAAA,IAChB;AAAA,IACD,gBAAgB,QAAQ;AACtBA,oBAAY,MAAA,MAAA,OAAA,qCAAA,UAAU,MAAM;AAC5B,WAAK,UAAU;AAAA,IAChB;AAAA,IACD,eAAe;AACb,WAAK,UAAU;AACf,WAAK,UAAU;AACf,WAAK,UAAU;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,cAAc;AACZ,YAAM,OAAO;AAAA,QACX,SAAS,KAAK;AAAA,QACd,SAAS,KAAK;AAAA,QACd,SAAS,KAAK;AAAA,QACd,aAAa,KAAK;AAAA;AAEpBA,oBAAY,MAAA,MAAA,OAAA,qCAAA,WAAW,IAAI;AAC3BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS,KAAK,UAAU,MAAM,MAAM,CAAC;AAAA,QACrC,YAAY;AAAA,MACd,CAAC;AAAA,IACF;AAAA,EACF;AACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClIA,GAAG,WAAW,eAAe;"}