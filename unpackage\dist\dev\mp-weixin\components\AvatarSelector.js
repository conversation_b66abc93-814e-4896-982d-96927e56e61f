"use strict";
const common_vendor = require("../common/vendor.js");
const _sfc_main = {
  name: "AvatarSelector",
  props: {
    // 头像值（字符串格式：图标类名或图片URL）
    value: {
      type: String,
      default: "fas fa-users"
      // 默认图标
    },
    // 用于生成文字头像的文本（如群组名称）
    text: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      currentAvatar: this.value || "fas fa-users",
      tempAvatar: null,
      // 临时选择的头像（字符串）
      selectorVisible: false,
      // 控制弹窗显示
      activeTab: 0,
      tabs: [
        { name: "图标", icon: "fas fa-icons" },
        { name: "上传", icon: "fas fa-upload" }
      ],
      // 预设头像分类
      presetAvatars: [
        {
          name: "学习考试",
          items: [
            { id: 1, icon: "fas fa-graduation-cap", bgColor: "#3B82F6" },
            // 毕业帽
            { id: 2, icon: "fas fa-book", bgColor: "#10B981" },
            // 书本
            { id: 3, icon: "fas fa-pencil-alt", bgColor: "#F59E0B" },
            // 铅笔
            { id: 4, icon: "fas fa-clipboard-check", bgColor: "#EF4444" },
            // 考试
            { id: 5, icon: "fas fa-certificate", bgColor: "#8B5CF6" },
            // 证书
            { id: 6, icon: "fas fa-trophy", bgColor: "#FBBF24" }
            // 奖杯
          ]
        },
        {
          name: "各行各业",
          items: [
            { id: 7, icon: "fas fa-user-md", bgColor: "#DC2626" },
            // 医生
            { id: 8, icon: "fas fa-balance-scale", bgColor: "#374151" },
            // 法律
            { id: 9, icon: "fas fa-chalkboard-teacher", bgColor: "#059669" },
            // 教师
            { id: 10, icon: "fas fa-code", bgColor: "#6366F1" },
            // 程序员
            { id: 11, icon: "fas fa-calculator", bgColor: "#F97316" },
            // 会计
            { id: 12, icon: "fas fa-hard-hat", bgColor: "#92400E" }
            // 工程师
          ]
        },
        {
          name: "通用图标",
          items: [
            { id: 13, icon: "fas fa-users", bgColor: "#06B6D4" },
            // 群组
            { id: 14, icon: "fas fa-star", bgColor: "#FBBF24" },
            // 星星
            { id: 15, icon: "fas fa-lightbulb", bgColor: "#84CC16" },
            // 灯泡
            { id: 16, icon: "fas fa-fire", bgColor: "#DC2626" },
            // 火焰
            { id: 17, icon: "fas fa-rocket", bgColor: "#8B5CF6" },
            // 火箭
            { id: 18, icon: "fas fa-heart", bgColor: "#F87171" }
            // 爱心
          ]
        }
      ]
    };
  },
  watch: {
    value: {
      handler(newVal) {
        this.currentAvatar = newVal || "fas fa-users";
      },
      immediate: true
    }
  },
  methods: {
    // 判断是否为自定义图片
    isCustomImage(avatar) {
      if (!avatar)
        return false;
      return avatar.startsWith("http") || avatar.startsWith("data:") || avatar.startsWith("/") || avatar.includes(".");
    },
    // 判断是否为预设图标
    isPresetIcon(avatar) {
      if (!avatar)
        return false;
      return avatar.startsWith("fas ") || avatar.startsWith("far ") || avatar.startsWith("fab ");
    },
    // 获取图标背景色
    getIconBgColor(iconClass) {
      const iconColorMap = {
        "fas fa-graduation-cap": "#3B82F6",
        "fas fa-book": "#10B981",
        "fas fa-pencil-alt": "#F59E0B",
        "fas fa-clipboard-check": "#EF4444",
        "fas fa-certificate": "#8B5CF6",
        "fas fa-trophy": "#FBBF24",
        "fas fa-user-md": "#DC2626",
        "fas fa-balance-scale": "#374151",
        "fas fa-chalkboard-teacher": "#059669",
        "fas fa-code": "#6366F1",
        "fas fa-calculator": "#F97316",
        "fas fa-hard-hat": "#92400E",
        "fas fa-users": "#06B6D4",
        "fas fa-star": "#FBBF24",
        "fas fa-lightbulb": "#84CC16",
        "fas fa-fire": "#DC2626",
        "fas fa-rocket": "#8B5CF6",
        "fas fa-heart": "#F87171"
      };
      return iconColorMap[iconClass] || "#06B6D4";
    },
    // 显示选择器
    showSelector() {
      this.tempAvatar = this.currentAvatar;
      this.selectorVisible = true;
    },
    // 关闭选择器
    closeSelector() {
      this.selectorVisible = false;
      this.tempAvatar = null;
    },
    // 切换Tab
    switchTab(index) {
      this.activeTab = index;
    },
    // 选择预设头像
    selectPreset(avatar) {
      this.tempAvatar = avatar.icon;
    },
    // 选择图片
    chooseImage() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          this.tempAvatar = tempFilePath;
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "选择图片失败",
            icon: "none"
          });
        }
      });
    },
    // 检查是否选中
    isSelected(value) {
      const avatar = this.tempAvatar || this.currentAvatar;
      return avatar === value;
    },
    // 确认选择
    confirmSelection() {
      if (this.tempAvatar) {
        this.currentAvatar = this.tempAvatar;
        this.$emit("input", this.currentAvatar);
        this.$emit("change", this.currentAvatar);
      }
      this.closeSelector();
    }
  },
  mounted() {
    if (!this.currentAvatar) {
      this.currentAvatar = "fas fa-users";
      this.$emit("input", this.currentAvatar);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $options.isCustomImage($data.currentAvatar)
  }, $options.isCustomImage($data.currentAvatar) ? {
    b: $data.currentAvatar
  } : $options.isPresetIcon($data.currentAvatar) ? {
    d: common_vendor.n($data.currentAvatar),
    e: $options.getIconBgColor($data.currentAvatar)
  } : {}, {
    c: $options.isPresetIcon($data.currentAvatar),
    f: common_vendor.o((...args) => $options.showSelector && $options.showSelector(...args)),
    g: $data.selectorVisible
  }, $data.selectorVisible ? common_vendor.e({
    h: common_vendor.o((...args) => $options.closeSelector && $options.closeSelector(...args)),
    i: common_vendor.f($data.tabs, (tab, index, i0) => {
      return {
        a: common_vendor.n(tab.icon),
        b: common_vendor.t(tab.name),
        c: index,
        d: common_vendor.n($data.activeTab === index ? "text-primary-500 border-b-2 border-primary-500" : "text-gray-500"),
        e: common_vendor.o(($event) => $options.switchTab(index), index)
      };
    }),
    j: $data.activeTab === 0
  }, $data.activeTab === 0 ? {
    k: common_vendor.f($data.presetAvatars, (category, k0, i0) => {
      return {
        a: common_vendor.t(category.name),
        b: common_vendor.f(category.items, (avatar, k1, i1) => {
          return common_vendor.e({
            a: common_vendor.n(avatar.icon),
            b: $options.isSelected(avatar.icon)
          }, $options.isSelected(avatar.icon) ? {} : {}, {
            c: common_vendor.n($options.isSelected(avatar.icon) ? "ring-2 ring-primary-500 ring-offset-1" : ""),
            d: avatar.bgColor,
            e: avatar.id,
            f: common_vendor.o(($event) => $options.selectPreset(avatar), avatar.id)
          });
        }),
        c: category.name
      };
    })
  } : {}, {
    l: $data.activeTab === 1
  }, $data.activeTab === 1 ? {
    m: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args))
  } : {}, {
    n: common_vendor.o((...args) => $options.closeSelector && $options.closeSelector(...args)),
    o: common_vendor.o((...args) => $options.confirmSelection && $options.confirmSelection(...args)),
    p: common_vendor.o(() => {
    }),
    q: common_vendor.o((...args) => $options.closeSelector && $options.closeSelector(...args))
  }) : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../.sourcemap/mp-weixin/components/AvatarSelector.js.map
