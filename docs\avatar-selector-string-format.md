# AvatarSelector 组件 - 字符串格式说明

## 概述

AvatarSelector 组件已更新为使用字符串格式存储头像数据，这样更便于数据库存储和数据传输。

## 数据格式变更

### 之前的对象格式
```javascript
{
  type: "preset",
  value: "fas fa-users",
  bgColor: "#06B6D4",
  textColor: "#FFFFFF"
}
```

### 现在的字符串格式
```javascript
"fas fa-users"  // 预设图标
"https://example.com/avatar.jpg"  // 自定义图片URL
"/static/uploads/avatar123.png"  // 本地图片路径
```

## 使用方法

### 基本用法
```vue
<template>
  <AvatarSelector 
    v-model="userAvatar" 
    text="用户名"
    @change="onAvatarChange"
  />
</template>

<script>
export default {
  data() {
    return {
      userAvatar: 'fas fa-users' // 字符串格式
    }
  },
  methods: {
    onAvatarChange(avatar) {
      console.log('新头像:', avatar); // 输出字符串
      this.userAvatar = avatar;
    }
  }
}
</script>
```

### 数据库存储

#### 表结构
```sql
CREATE TABLE users (
  id INT PRIMARY KEY,
  name VARCHAR(100),
  avatar VARCHAR(255), -- 存储头像字符串
  created_at TIMESTAMP
);
```

#### 存储示例
```sql
INSERT INTO users (name, avatar) VALUES 
('张三', 'fas fa-users'),
('李四', 'fas fa-graduation-cap'),
('王五', 'https://cdn.example.com/avatar/user123.jpg');
```

## 头像类型识别

组件会自动识别头像类型：

### 预设图标
- 格式：以 `fas `、`far ` 或 `fab ` 开头的字符串
- 示例：`"fas fa-users"`、`"fas fa-graduation-cap"`
- 显示：带背景色的图标

### 自定义图片
- 格式：包含 `http`、`data:`、`/` 或 `.` 的字符串
- 示例：`"https://example.com/avatar.jpg"`、`"/static/avatar.png"`
- 显示：图片

### 默认头像
- 格式：空字符串或不匹配上述格式的字符串
- 显示：默认的用户图标

## 预设图标列表

### 学习考试类
- `fas fa-graduation-cap` - 毕业帽 (#3B82F6)
- `fas fa-book` - 书本 (#10B981)
- `fas fa-pencil-alt` - 铅笔 (#F59E0B)
- `fas fa-clipboard-check` - 考试 (#EF4444)
- `fas fa-certificate` - 证书 (#8B5CF6)
- `fas fa-trophy` - 奖杯 (#FBBF24)

### 各行各业类
- `fas fa-user-md` - 医生 (#DC2626)
- `fas fa-balance-scale` - 法律 (#374151)
- `fas fa-chalkboard-teacher` - 教师 (#059669)
- `fas fa-code` - 程序员 (#6366F1)
- `fas fa-calculator` - 会计 (#F97316)
- `fas fa-hard-hat` - 工程师 (#92400E)

### 通用图标类
- `fas fa-users` - 群组 (#06B6D4)
- `fas fa-star` - 星星 (#FBBF24)
- `fas fa-lightbulb` - 灯泡 (#84CC16)
- `fas fa-fire` - 火焰 (#DC2626)
- `fas fa-rocket` - 火箭 (#8B5CF6)
- `fas fa-heart` - 爱心 (#F87171)

## 迁移指南

### 从对象格式迁移到字符串格式

```javascript
// 旧格式
const oldAvatar = {
  type: "preset",
  value: "fas fa-users",
  bgColor: "#06B6D4",
  textColor: "#FFFFFF"
};

// 新格式
const newAvatar = "fas fa-users";

// 迁移函数
function migrateAvatar(oldAvatar) {
  if (typeof oldAvatar === 'string') {
    return oldAvatar; // 已经是新格式
  }
  
  if (oldAvatar && oldAvatar.value) {
    return oldAvatar.value; // 提取 value 字段
  }
  
  return 'fas fa-users'; // 默认值
}
```

### 数据库迁移

```sql
-- 如果之前存储的是JSON格式，需要迁移
UPDATE users 
SET avatar = JSON_UNQUOTE(JSON_EXTRACT(avatar, '$.value'))
WHERE JSON_VALID(avatar) = 1;
```

## 优势

1. **数据库友好**：直接存储字符串，无需JSON字段
2. **传输效率**：数据量更小，传输更快
3. **简化逻辑**：减少数据处理复杂度
4. **向后兼容**：组件内部处理类型识别
5. **易于调试**：直观的字符串值，便于查看和调试

## 注意事项

1. 确保图片URL的有效性和可访问性
2. 预设图标依赖FontAwesome图标库
3. 自定义图片建议使用CDN或稳定的图片服务
4. 空字符串会显示默认头像
