{"version": 3, "file": "create.js", "sources": ["pages/group/create.vue", "D:/Program Files/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZ3JvdXAvY3JlYXRlLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"h-screen bg-gray-50 flex flex-col\">\n    <!-- 自定义导航栏 -->\n    <view class=\"relative w-full mb-4\">\n      <view\n        class=\"w-full bg-gradient-to-r bg-primary-500 to-primary-600 relative overflow-hidden\"\n        :style=\"navBarStyle\"\n      >\n        <!-- 导航内容 -->\n        <view class=\"px-4 py-6 relative z-10\">\n          <view class=\"text-center\">\n            <view class=\"flex items-center justify-center mb-2\">\n              <view\n                class=\"w-10 h-10 rounded-full bg-white bg-opacity-20 flex items-center justify-center mr-3 backdrop-blur-sm\"\n              >\n                <i class=\"fas fa-plus text-white text-lg\"></i>\n              </view>\n              <text class=\"text-white text-xl font-bold\">创建群组</text>\n            </view>\n            <text class=\"text-white text-sm opacity-90\"\n              >填写群组信息，创建专属学习空间</text\n            >\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 表单内容 -->\n    <view class=\"flex-1 px-4 pb-6\">\n      <view class=\"bg-white rounded-xl shadow-sm p-5\">\n        <!-- 群头像 -->\n        <view class=\"mb-6\">\n          <text class=\"text-base font-semibold text-gray-800 mb-3 block\"\n            >群头像</text\n          >\n          <AvatarSelector\n            v-model=\"form.avatar\"\n            :text=\"form.name\"\n            @change=\"onAvatarChange\"\n          />\n        </view>\n\n        <!-- 群名称 -->\n        <view class=\"mb-6\">\n          <view class=\"flex items-center mb-3\">\n            <text class=\"text-base font-semibold text-gray-800\">群名称</text>\n            <text class=\"text-red-500 ml-1\">*</text>\n          </view>\n          <input\n            v-model=\"form.name\"\n            placeholder=\"请输入群名称（16字以内）\"\n            maxlength=\"16\"\n            class=\"w-full h-auto px-4 py-3 border border-gray-200 rounded-lg text-base\"\n          />\n          <view class=\"flex justify-between mt-1\">\n            <text class=\"text-xs text-red-500\" v-if=\"errors.name\">{{\n              errors.name\n            }}</text>\n            <text class=\"text-xs text-gray-400 ml-auto\"\n              >{{ form.name.length }}/16</text\n            >\n          </view>\n        </view>\n\n        <!-- 群介绍 -->\n        <view class=\"mb-6\">\n          <view class=\"flex items-center mb-3\">\n            <text class=\"text-base font-semibold text-gray-800\">群介绍</text>\n            <text class=\"text-red-500 ml-1\">*</text>\n          </view>\n          <textarea\n            v-model=\"form.description\"\n            placeholder=\"请输入群介绍（80字以内）\"\n            maxlength=\"80\"\n            class=\"w-full px-4 py-3 border border-gray-200 rounded-lg text-base h-20\"\n          />\n          <view class=\"flex justify-between mt-1\">\n            <text class=\"text-xs text-red-500\" v-if=\"errors.description\">{{\n              errors.description\n            }}</text>\n            <text class=\"text-xs text-gray-400 ml-auto\"\n              >{{ form.description.length }}/80</text\n            >\n          </view>\n        </view>\n\n        <!-- 群公告 -->\n        <view class=\"mb-6\">\n          <text class=\"text-base font-semibold text-gray-800 mb-3 block\"\n            >群公告</text\n          >\n          <textarea\n            v-model=\"form.notice\"\n            placeholder=\"请输入群公告（选填）\"\n            class=\"w-full px-4 py-3 border border-gray-200 rounded-lg text-base h-20\"\n          />\n        </view>\n\n        <!-- 加入方式 -->\n        <view class=\"mb-6\">\n          <view class=\"flex items-center mb-3\">\n            <text class=\"text-base font-semibold text-gray-800\">加入方式</text>\n            <text class=\"text-red-500 ml-1\">*</text>\n          </view>\n          <view class=\"space-y-3\">\n            <view\n              v-for=\"option in joinOptions\"\n              :key=\"option.value\"\n              class=\"flex items-center p-3 border border-gray-200 rounded-lg\"\n              :class=\"\n                form.joinType === option.value\n                  ? 'border-primary-500 bg-primary-50'\n                  : ''\n              \"\n              @click=\"form.joinType = option.value\"\n            >\n              <view\n                class=\"w-5 h-5 rounded-full border-2 flex items-center justify-center mr-3\"\n                :class=\"\n                  form.joinType === option.value\n                    ? 'border-primary-500'\n                    : 'border-gray-300'\n                \"\n              >\n                <view\n                  v-if=\"form.joinType === option.value\"\n                  class=\"w-2.5 h-2.5 rounded-full bg-primary-500\"\n                ></view>\n              </view>\n              <view class=\"flex-1\">\n                <text class=\"text-base font-medium text-gray-800 block\">{{\n                  option.label\n                }}</text>\n                <text class=\"text-sm text-gray-500\">{{ option.desc }}</text>\n              </view>\n            </view>\n          </view>\n        </view>\n\n        <!-- 口令设置 -->\n        <view v-if=\"form.joinType === 3\" class=\"mb-6\">\n          <text class=\"text-base font-semibold text-gray-800 mb-3 block\"\n            >入群口令</text\n          >\n          <input\n            v-model=\"form.password\"\n            placeholder=\"请输入6位数字口令\"\n            maxlength=\"6\"\n            type=\"number\"\n            class=\"h-auto w-full px-4 py-3 border border-gray-200 rounded-lg text-base\"\n          />\n          <text class=\"text-xs text-red-500 mt-1\" v-if=\"errors.password\">{{\n            errors.password\n          }}</text>\n        </view>\n\n        <!-- 创建按钮 -->\n        <view class=\"space-y-3\">\n          <button\n            class=\"w-full bg-primary-500 text-white py-3 rounded-lg text-base font-medium\"\n            @click=\"submitForm\"\n            :disabled=\"submitting\"\n          >\n            <text v-if=\"submitting\">创建中...</text>\n            <text v-else>创建群组</text>\n          </button>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport AvatarSelector from \"@/components/AvatarSelector.vue\";\n\nexport default {\n  components: {\n    AvatarSelector,\n  },\n  data() {\n    return {\n      navBarStyle: {},\n      navBarButtonStyle: {},\n      submitting: false,\n      form: {\n        name: \"\",\n        avatar: \"fas fa-users\", // 默认头像为群组图标\n        description: \"\",\n        notice: \"\",\n        joinType: 1,\n        password: \"\",\n      },\n      errors: {},\n      joinOptions: [\n        { value: 1, label: \"不限制\", desc: \"任何人都可以直接加入群组\" },\n        { value: 2, label: \"审核后加入\", desc: \"需要群主或管理员审核通过\" },\n        { value: 3, label: \"口令加入\", desc: \"需要输入正确的6位数字口令\" },\n      ],\n    };\n  },\n  methods: {\n    setNavBarButtonStyle() {\n      let style = {};\n      // #ifdef MP-WEIXIN\n      const menuButton = uni.getMenuButtonBoundingClientRect();\n      style = {\n        position: \"absolute\",\n        left: \"16px\",\n        top: menuButton.top + \"px\",\n        width: menuButton.height + \"px\",\n        height: menuButton.height + \"px\",\n        \"z-index\": 20,\n      };\n      // #endif\n      // #ifdef H5\n      style = {\n        position: \"absolute\",\n        left: \"16px\",\n        top: \"16px\",\n        width: \"40px\",\n        height: \"40px\",\n        \"z-index\": 20,\n      };\n      // #endif\n      this.navBarButtonStyle = style;\n    },\n    goBack() {\n      uni.navigateBack();\n    },\n    onAvatarChange(avatar) {\n      this.form.avatar = avatar;\n      console.log(\"头像已更新:\", avatar);\n    },\n    validateForm() {\n      this.errors = {};\n\n      if (!this.form.name.trim()) {\n        this.errors.name = \"请输入群名称\";\n        return false;\n      }\n      if (this.form.name.length > 16) {\n        this.errors.name = \"群名称不能超过16个字符\";\n        return false;\n      }\n\n      if (!this.form.description.trim()) {\n        this.errors.description = \"请输入群介绍\";\n        return false;\n      }\n      if (this.form.description.length > 80) {\n        this.errors.description = \"群介绍不能超过80个字符\";\n        return false;\n      }\n\n      if (this.form.joinType === 3) {\n        if (!this.form.password || this.form.password.length !== 6) {\n          this.errors.password = \"请输入6位数字口令\";\n          return false;\n        }\n        if (!/^\\d{6}$/.test(this.form.password)) {\n          this.errors.password = \"口令必须是6位数字\";\n          return false;\n        }\n      }\n\n      return true;\n    },\n    async submitForm() {\n      if (!this.validateForm()) {\n        return;\n      }\n\n      this.submitting = true;\n\n      try {\n        // TODO: 调用创建群组接口\n        // const res = await this.$reqPost('/front/group/create', this.form);\n\n        uni.showToast({\n          title: \"创建成功\",\n          icon: \"success\",\n        });\n\n        setTimeout(() => {\n          uni.navigateBack();\n        }, 1500);\n      } catch (error) {\n        uni.showToast({\n          title: error.message || \"创建失败\",\n          icon: \"none\",\n        });\n      } finally {\n        this.submitting = false;\n      }\n    },\n  },\n  onLoad() {},\n};\n</script>\n\n<style scoped></style>\n", "import MiniProgramPage from 'E:/work/code/edu/edu-personal-uniapp/pages/group/create.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA6KA,MAAK,iBAAkB,MAAW;AAElC,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,aAAa,CAAE;AAAA,MACf,mBAAmB,CAAE;AAAA,MACrB,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,MAAM;AAAA,QACN,QAAQ;AAAA;AAAA,QACR,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,UAAU;AAAA,MACX;AAAA,MACD,QAAQ,CAAE;AAAA,MACV,aAAa;AAAA,QACX,EAAE,OAAO,GAAG,OAAO,OAAO,MAAM,eAAgB;AAAA,QAChD,EAAE,OAAO,GAAG,OAAO,SAAS,MAAM,eAAgB;AAAA,QAClD,EAAE,OAAO,GAAG,OAAO,QAAQ,MAAM,gBAAiB;AAAA,MACnD;AAAA;EAEJ;AAAA,EACD,SAAS;AAAA,IACP,uBAAuB;AACrB,UAAI,QAAQ,CAAA;AAEZ,YAAM,aAAaA,oBAAI;AACvB,cAAQ;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,QACN,KAAK,WAAW,MAAM;AAAA,QACtB,OAAO,WAAW,SAAS;AAAA,QAC3B,QAAQ,WAAW,SAAS;AAAA,QAC5B,WAAW;AAAA;AAab,WAAK,oBAAoB;AAAA,IAC1B;AAAA,IACD,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,IACD,eAAe,QAAQ;AACrB,WAAK,KAAK,SAAS;AACnBA,oBAAY,MAAA,MAAA,OAAA,iCAAA,UAAU,MAAM;AAAA,IAC7B;AAAA,IACD,eAAe;AACb,WAAK,SAAS;AAEd,UAAI,CAAC,KAAK,KAAK,KAAK,KAAI,GAAI;AAC1B,aAAK,OAAO,OAAO;AACnB,eAAO;AAAA,MACT;AACA,UAAI,KAAK,KAAK,KAAK,SAAS,IAAI;AAC9B,aAAK,OAAO,OAAO;AACnB,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,KAAK,KAAK,YAAY,KAAI,GAAI;AACjC,aAAK,OAAO,cAAc;AAC1B,eAAO;AAAA,MACT;AACA,UAAI,KAAK,KAAK,YAAY,SAAS,IAAI;AACrC,aAAK,OAAO,cAAc;AAC1B,eAAO;AAAA,MACT;AAEA,UAAI,KAAK,KAAK,aAAa,GAAG;AAC5B,YAAI,CAAC,KAAK,KAAK,YAAY,KAAK,KAAK,SAAS,WAAW,GAAG;AAC1D,eAAK,OAAO,WAAW;AACvB,iBAAO;AAAA,QACT;AACA,YAAI,CAAC,UAAU,KAAK,KAAK,KAAK,QAAQ,GAAG;AACvC,eAAK,OAAO,WAAW;AACvB,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,aAAO;AAAA,IACR;AAAA,IACD,MAAM,aAAa;AACjB,UAAI,CAAC,KAAK,gBAAgB;AACxB;AAAA,MACF;AAEA,WAAK,aAAa;AAElB,UAAI;AAIFA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAED,mBAAW,MAAM;AACfA,wBAAG,MAAC,aAAY;AAAA,QACjB,GAAE,IAAI;AAAA,MACP,SAAO,OAAO;AACdA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,QACR,CAAC;AAAA,MACH,UAAU;AACR,aAAK,aAAa;AAAA,MACpB;AAAA,IACD;AAAA,EACF;AAAA,EACD,SAAS;AAAA,EAAE;AACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxSA,GAAG,WAAW,eAAe;"}